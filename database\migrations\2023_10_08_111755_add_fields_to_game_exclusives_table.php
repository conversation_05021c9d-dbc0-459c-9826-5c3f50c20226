<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create the table if it doesn't exist
        if (!Schema::hasTable('game_exclusives')) {
            Schema::create('game_exclusives', function (Blueprint $table) {
                $table->id();
                $table->timestamps();
            });
        }

        // Add the fields if they don't exist
        Schema::table('game_exclusives', function (Blueprint $table) {
            if (!Schema::hasColumn('game_exclusives', 'loseResults')) {
                $table->text('loseResults');
            }
            if (!Schema::hasColumn('game_exclusives', 'demoWinResults')) {
                $table->text('demoWinResults');
            }
            if (!Schema::hasColumn('game_exclusives', 'winResults')) {
                $table->text('winResults');
            }
            if (!Schema::hasColumn('game_exclusives', 'iconsJson')) {
                $table->text('iconsJson');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('game_exclusives', function (Blueprint $table) {
            $table->dropColumn('loseResults');
            $table->dropColumn('demoWinResults');
            $table->dropColumn('winResults');
            $table->dropColumn('iconsJson');
        });
    }
};
