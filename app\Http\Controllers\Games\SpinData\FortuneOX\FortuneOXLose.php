<?php

namespace App\Http\Controllers\Games\SpinData\FortuneOX;

class FortuneOXLose
{
    /**
     * @return array
     */
    public static function getLose(): array
    {
        return [
            [
                ["Symbol_5", "Symbol_3", "Symbol_6", "Symbol_2", "Symbol_2", "Symbol_1", "Symbol_3", "Symbol_4", "Symbol_5", "_blank", "Symbol_3", "_blank"], [], [], [], 1, 0
            ],

            [
                ["Symbol_5", "Symbol_4", "Symbol_3", "Symbol_3", "Symbol_3", "Symbol_2", "Symbol_2", "Symbol_0", "Symbol_6", "_blank", "Symbol_1", "_blank"], [], [], [], 1, 0
            ],

            [
                ["Symbol_5", "Symbol_1", "Symbol_3", "Symbol_1", "<PERSON>ymbol_4", "<PERSON>ymbol_6", "Symbol_3", "<PERSON>ym<PERSON>_6", "Symbol_0", "_blank", "Symbol_1", "_blank"], [], [], [], 1, 0
            ],

            [
                ["Symbol_5", "Symbol_2", "Symbol_3", "Symbol_6", "Symbol_3", "Symbol_1", "Symbol_5", "Symbol_1", "Symbol_1", "_blank", "Symbol_4", "_blank"], [], [], [], 1, 0
            ],
            [
                ["Symbol_3", "Symbol_6", "Symbol_5", "Symbol_1", "Symbol_6", "Symbol_6", "Symbol_6", "Symbol_5", "Symbol_2", "_blank", "Symbol_0", "_blank"], [], [], [], 1, 0
            ],
            [
                ["Symbol_2", "Symbol_1", "Symbol_4", "Symbol_5", "Symbol_3", "Symbol_6", "Symbol_5", "Symbol_3", "Symbol_3", "_blank", "Symbol_1", "_blank"], [], [], [], 1, 0
            ],
            [
                ["Symbol_4", "Symbol_1", "Symbol_2", "Symbol_3", "Symbol_4", "Symbol_3", "Symbol_6", "Symbol_4", "Symbol_3", "_blank", "Symbol_5", "_blank"], [], [], [], 1, 0
            ],
            [
                ["Symbol_2", "Symbol_1", "Symbol_4", "Symbol_2", "Symbol_6", "Symbol_2", "Symbol_1", "Symbol_5", "Symbol_4", "_blank", "Symbol_3", "_blank"], [], [], [], 1, 0
            ],
            [
                ["Symbol_6", "Symbol_1", "Symbol_5", "Symbol_3", "Symbol_1", "Symbol_3", "Symbol_3", "Symbol_6", "Symbol_5", "_blank", "Symbol_3", "_blank"], [], [], [], 1, 0
            ],
            [
                ["Symbol_3", "Symbol_4", "Symbol_4", "Symbol_4", "Symbol_4", "Symbol_3", "Symbol_5", "Symbol_1", "Symbol_6", "_blank", "Symbol_5", "_blank"], [], [], [], 1, 0
            ],
            [
                ["Symbol_3", "Symbol_5", "Symbol_1", "Symbol_6", "Symbol_5", "Symbol_4", "Symbol_6", "Symbol_1", "Symbol_2", "_blank", "Symbol_2", "_blank"], [], [], [], 1, 0
            ],
            [
                ["Symbol_2", "Symbol_1", "Symbol_4", "Symbol_5", "Symbol_3", "Symbol_6", "Symbol_5", "Symbol_3", "Symbol_1", "_blank", "Symbol_1", "_blank"], [], [], [], 1, 0
            ],
            [
                ["Symbol_2", "Symbol_1", "Symbol_4", "Symbol_5", "Symbol_3", "Symbol_6", "Symbol_5", "Symbol_3", "Symbol_2", "_blank", "Symbol_1", "_blank"], [], [], [], 1, 0
            ],
            [
                ["Symbol_4", "Symbol_6", "Symbol_3", "Symbol_6", "Symbol_4", "Symbol_4", "Symbol_5", "Symbol_2", "Symbol_3", "_blank", "Symbol_2", "_blank"], [], [], [], 1, 0
            ],
            [
                ["Symbol_2", "Symbol_4", "Symbol_1", "Symbol_3", "Symbol_4", "Symbol_6", "Symbol_6", "Symbol_0", "Symbol_4", "_blank", "Symbol_3", "_blank"], [], [], [], 1, 0
            ],
            [
                ["Symbol_3", "Symbol_4", "Symbol_1", "Symbol_6", "Symbol_4", "Symbol_5", "Symbol_2", "Symbol_1", "Symbol_5", "_blank", "Symbol_6", "_blank"], [], [], [], 1, 0
            ],
            [
                ["Symbol_6", "Symbol_0", "Symbol_1", "Symbol_3", "Symbol_4", "Symbol_2", "Symbol_3", "Symbol_2", "Symbol_6", "_blank", "Symbol_1", "_blank"], [], [], [], 1, 0
            ],
            [
                ["Symbol_3", "Symbol_6", "Symbol_5", "Symbol_5", "Symbol_3", "Symbol_4", "Symbol_5", "Symbol_2", "Symbol_1", "_blank", "Symbol_2", "_blank"], [], [], [], 1, 0
            ],

        ];
    }
}
