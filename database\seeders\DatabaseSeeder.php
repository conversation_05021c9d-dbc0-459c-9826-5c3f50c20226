<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Banner;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create sample banners
        if (Banner::count() == 0) {
            $banners = [
                [
                    'image' => '/assets/images/sliders/banner-1.jpg',
                    'type' => 'carousel',
                    'description' => 'Welcome to ViperPRO Casino',
                    'link' => '#'
                ],
                [
                    'image' => '/assets/images/sliders/banner-2.jpg',
                    'type' => 'carousel',
                    'description' => 'Play the best casino games',
                    'link' => '#'
                ],
                [
                    'image' => '/assets/images/sliders/banner-3.jpg',
                    'type' => 'home',
                    'description' => 'Join now and get bonus',
                    'link' => '#'
                ],
            ];

            foreach ($banners as $banner) {
                Banner::create($banner);
            }
        }
    }
}
