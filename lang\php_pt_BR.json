{"auth.failed": "<PERSON>ssas credenciais não foram encontradas em nossos registros.", "auth.password": "A senha informada está incorreta.", "auth.throttle": "Muitas tentativas de login. Tente novamente em :seconds segundos.", "base.list_user": "Lista de Usuários", "base.view_user": "<PERSON><PERSON>", "base.edit_user": "<PERSON><PERSON>", "base.change_password": "<PERSON><PERSON>", "base.demo": "Demo", "pagination.previous": "&laquo; Anterior", "pagination.next": "Próximo &raquo;", "passwords.password": "A senha e a confirmação devem combinar e possuir pelo menos seis caracteres.", "passwords.reset": "Sua senha foi redefinida!", "passwords.sent": "Enviamos seu link de redefinição de senha por e-mail!", "passwords.throttled": "Aguarde antes de tentar novamente.", "passwords.token": "Este token de redefinição de senha é inválido.", "passwords.user": "Não encontramos um usuário com esse endereço de e-mail.", "validation.accepted": "O campo :attribute deve ser aceito.", "validation.accepted_if": "O :attribute deve ser aceito quando :other for :value.", "validation.active_url": "O campo :attribute não é uma URL válida.", "validation.after": "O campo :attribute deve ser uma data posterior a :date.", "validation.after_or_equal": "O campo :attribute deve ser uma data posterior ou igual a :date.", "validation.alpha": "O campo :attribute só pode conter letras.", "validation.alpha_dash": "O campo :attribute só pode conter letras, números e traços.", "validation.alpha_num": "O campo :attribute só pode conter letras e números.", "validation.array": "O campo :attribute deve ser uma matriz.", "validation.before": "O campo :attribute deve ser uma data anterior :date.", "validation.before_or_equal": "O campo :attribute deve ser uma data anterior ou igual a :date.", "validation.between.numeric": "O campo :attribute deve ser entre :min e :max.", "validation.between.file": "O campo :attribute deve ser entre :min e :max kilobytes.", "validation.between.string": "O campo :attribute deve ser entre :min e :max caracteres.", "validation.between.array": "O campo :attribute deve ter entre :min e :max itens.", "validation.boolean": "O campo :attribute deve ser verdadeiro ou falso.", "validation.confirmed": "O campo :attribute de confirmação não confere.", "validation.current_password": "A senha está incorreta.", "validation.date": "O campo :attribute não é uma data válida.", "validation.date_equals": "O campo :attribute deve ser uma data igual a :date.", "validation.date_format": "O campo :attribute não corresponde ao formato :format.", "validation.declined": "O :attribute deve ser recusado.", "validation.declined_if": "O :attribute deve ser recusado quando :other for :value.", "validation.different": "Os campos :attribute e :other devem ser diferentes.", "validation.digits": "O campo :attribute deve ter :digits dígitos.", "validation.digits_between": "O campo :attribute deve ter entre :min e :max dígitos.", "validation.dimensions": "O campo :attribute tem dimensões de imagem inválidas.", "validation.distinct": "O campo :attribute campo tem um valor duplicado.", "validation.doesnt_start_with": "O :attribute não pode começar com um dos seguintes: :values.", "validation.email": "O campo :attribute deve ser um endereço de e-mail válido.", "validation.ends_with": "O campo :attribute deve terminar com um dos seguintes: :values", "validation.enum": "O :attribute selecionado é inválido.", "validation.exists": "O campo :attribute selecionado é inválido.", "validation.file": "O campo :attribute deve ser um arquivo.", "validation.filled": "O campo :attribute deve ter um valor.", "validation.gt.numeric": "O campo :attribute deve ser maior que :value.", "validation.gt.file": "O campo :attribute deve ser maior que :value kilobytes.", "validation.gt.string": "O campo :attribute deve ser maior que :value caracteres.", "validation.gt.array": "O campo :attribute deve conter mais de :value itens.", "validation.gte.numeric": "O campo :attribute deve ser maior ou igual a :value.", "validation.gte.file": "O campo :attribute deve ser maior ou igual a :value kilobytes.", "validation.gte.string": "O campo :attribute deve ser maior ou igual a :value caracteres.", "validation.gte.array": "O campo :attribute deve conter :value itens ou mais.", "validation.image": "O campo :attribute deve ser uma imagem.", "validation.in": "O campo :attribute selecionado é inválido.", "validation.in_array": "O campo :attribute não existe em :other.", "validation.integer": "O campo :attribute deve ser um número inteiro.", "validation.ip": "O campo :attribute deve ser um endereço de IP válido.", "validation.ipv4": "O campo :attribute deve ser um endereço IPv4 válido.", "validation.ipv6": "O campo :attribute deve ser um endereço IPv6 válido.", "validation.json": "O campo :attribute deve ser uma string JSON válida.", "validation.lt.numeric": "O campo :attribute deve ser menor que :value.", "validation.lt.file": "O campo :attribute deve ser menor que :value kilobytes.", "validation.lt.string": "O campo :attribute deve ser menor que :value caracteres.", "validation.lt.array": "O campo :attribute deve conter menos de :value itens.", "validation.lte.numeric": "O campo :attribute deve ser menor ou igual a :value.", "validation.lte.file": "O campo :attribute deve ser menor ou igual a :value kilobytes.", "validation.lte.string": "O campo :attribute deve ser menor ou igual a :value caracteres.", "validation.lte.array": "O campo :attribute não deve conter mais que :value itens.", "validation.max.numeric": "O campo :attribute não pode ser superior a :max.", "validation.max.file": "O campo :attribute não pode ser superior a :max kilobytes.", "validation.max.string": "O campo :attribute não pode ser superior a :max caracteres.", "validation.max.array": "O campo :attribute não pode ter mais do que :max itens.", "validation.max_digits": "O campo :attribute não pode ser superior a :max dígitos", "validation.mimes": "O campo :attribute deve ser um arquivo do tipo: :values.", "validation.mimetypes": "O campo :attribute deve ser um arquivo do tipo: :values.", "validation.min.numeric": "O campo :attribute deve ser pelo menos :min.", "validation.min.file": "O campo :attribute deve ter pelo menos :min kilobytes.", "validation.min.string": "O campo :attribute deve ter pelo menos :min caracteres.", "validation.min.array": "O campo :attribute deve ter pelo menos :min itens.", "validation.missing_with": "O campo :attribute não deve estar presente quando houver :values.", "validation.min_digits": "O campo :attribute deve ter pelo menos :min dígitos", "validation.not_in": "O campo :attribute selecionado é inválido.", "validation.multiple_of": "O campo :attribute deve ser um múltiplo de :value.", "validation.not_regex": "O campo :attribute possui um formato inválido.", "validation.numeric": "O campo :attribute deve ser um número.", "validation.password.letters": "O campo :attribute deve conter pelo menos uma letra.", "validation.password.mixed": "O campo :attribute deve conter pelo menos uma letra maiúscula e uma letra minúscula.", "validation.password.numbers": "O campo :attribute deve conter pelo menos um número.", "validation.password.symbols": "O campo :attribute deve conter pelo menos um símbolo.", "validation.password.uncompromised": "A senha que você inseriu em :attribute está em um vazamento de dados. Por favor escolha uma senha diferente.", "validation.present": "O campo :attribute deve estar presente.", "validation.regex": "O campo :attribute tem um formato inválido.", "validation.required": "O campo :attribute é obrigatório.", "validation.required_array_keys": "O campo :attribute deve conter entradas para: :values.", "validation.required_if": "O campo :attribute é obrigatório quando :other for :value.", "validation.required_unless": "O campo :attribute é obrigatório exceto quando :other for :values.", "validation.required_with": "O campo :attribute é obrigatório quando :values está presente.", "validation.required_with_all": "O campo :attribute é obrigatório quando :values está presente.", "validation.required_without": "O campo :attribute é obrigatório quando :values não está presente.", "validation.required_without_all": "O campo :attribute é obrigatório quando nenhum dos :values estão presentes.", "validation.prohibited": "O campo :attribute é proibido.", "validation.prohibited_if": "O campo :attribute é proibido quando :other for :value.", "validation.prohibited_unless": "O campo :attribute é proibido exceto quando :other for :values.", "validation.prohibits": "O campo :attribute proíbe :other de estar presente.", "validation.same": "Os campos :attribute e :other devem corresponder.", "validation.size.numeric": "O campo :attribute deve ser :size.", "validation.size.file": "O campo :attribute deve ser :size kilobytes.", "validation.size.string": "O campo :attribute deve ser :size caracteres.", "validation.size.array": "O campo :attribute deve conter :size itens.", "validation.starts_with": "O campo :attribute deve começar com um dos seguintes valores: :values", "validation.string": "O campo :attribute deve ser uma string.", "validation.timezone": "O campo :attribute deve ser uma zona válida.", "validation.unique": "O campo :attribute já está sendo utilizado.", "validation.uploaded": "Ocorreu uma falha no upload do campo :attribute.", "validation.url": "O campo :attribute tem um formato inválido.", "validation.uuid": "O campo :attribute deve ser um UUID válido.", "validation.custom.attribute-name.rule-name": "custom-message", "validation.attributes.address": "endereço", "validation.attributes.age": "idade", "validation.attributes.body": "<PERSON><PERSON><PERSON><PERSON>", "validation.attributes.cell": "<PERSON><PERSON><PERSON><PERSON>", "validation.attributes.city": "cidade", "validation.attributes.country": "país", "validation.attributes.date": "data", "validation.attributes.day": "dia", "validation.attributes.excerpt": "resumo", "validation.attributes.first_name": "primeiro nome", "validation.attributes.gender": "<PERSON><PERSON><PERSON><PERSON>", "validation.attributes.marital_status": "estado civil", "validation.attributes.profession": "profiss<PERSON>", "validation.attributes.nationality": "nacionalidade", "validation.attributes.hour": "hora", "validation.attributes.last_name": "sobrenome", "validation.attributes.message": "mensagem", "validation.attributes.minute": "minuto", "validation.attributes.mobile": "celular", "validation.attributes.month": "mês", "validation.attributes.name": "nome", "validation.attributes.zipcode": "cep", "validation.attributes.company_name": "razão social", "validation.attributes.neighborhood": "bairro", "validation.attributes.number": "número", "validation.attributes.password": "<PERSON><PERSON>a", "validation.attributes.phone": "telefone", "validation.attributes.second": "segundo", "validation.attributes.sex": "sexo", "validation.attributes.state": "estado", "validation.attributes.street": "rua", "validation.attributes.subject": "assunto", "validation.attributes.text": "texto", "validation.attributes.time": "hora", "validation.attributes.title": "t<PERSON><PERSON><PERSON>", "validation.attributes.username": "<PERSON><PERSON><PERSON><PERSON>", "validation.attributes.year": "ano", "validation.attributes.description": "descrição", "validation.attributes.password_confirmation": "confirmação da senha", "validation.attributes.current_password": "se<PERSON>a atual", "validation.attributes.complement": "complemento", "validation.attributes.modality": "modalidade", "validation.attributes.category": "categoria", "validation.attributes.blood_type": "tipo sanguíneo", "validation.attributes.birth_date": "data de nascimento"}