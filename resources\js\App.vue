<template>
    <div>
        <h1 style="color: red; font-size: 24px; text-align: center; margin: 50px;">Vue.js is Working! ViperPRO Casino</h1>
        <RouterView v-slot="{ Component, route }">
            <Transition name="page-opacity" mode="out-in">
                <div :key="route.name">
                    <component :is="Component"></component>
                </div>
            </Transition>
        </RouterView>
    </div>
</template>

<script>
import { onMounted } from "vue";
import { RouterView } from "vue-router";

export default {
    props: [],
    components: { RouterView },
    data() {
        return {

        }
    },
    setup(props) {
        onMounted(() => {
        });

        return {};
    },
    computed: {

    },
    mounted() {

    },
    methods: {

    },
    watch: {

    },
};
</script>

<style scoped>

</style>
